import os
os.environ["BEZIER_NO_EXTENSION"] = "1"

import asyncio
import random
import traceback
from pathlib import Path

import playwright
from playwright.async_api import async_playwright
from python_ghost_cursor.playwright_async import create_cursor
from playwright_stealth import stealth_async
from pathlib import Path
import shutil
import json
from datetime import datetime, timedelta
import pytz
from antidetect.antidetect_browser import Antidetect
from library.steam_library.proxies import get_proxy
import query_builder as qb
from library.steam_library.queries.sldb import sldb_api

USER_DATA_DIR = "./tmp/test-user-data-dir-steam"
PROXY_TIMEOUT = 8
libraries = qb.Schema('sldb', db_name='steam_test')
steam_libraries = qb.Schema('library', db_name='steam')

async def clear_user_data_dir():
    dirpath = Path(USER_DATA_DIR)
    if dirpath.exists() and dirpath.is_dir():
        shutil.rmtree(dirpath)

class FailedToLoadInitialPage(Exception):
    pass

async def wait_and_click(page, cursor, selector):
    await page.wait_for_selector(selector)
    await page.click(selector)


async def cookie_interceptor(request, url, future):
    url = "https://steamcommunity.com/profiles/"
    if request.url.startswith(url):
        try:
            headers = await request.all_headers()
            cookie = headers.get("cookie")
            if not cookie:
                raise Exception("Cookie not found")
            if not future.done():
                future.set_result(cookie)
        except playwright._impl._api_types.Error as e:
            print(f"cookie_interceptor: {e}")
        except Exception as e:
            print(f"cookie_interceptor: {e}")

async def login(page, email, username, password):
    print("logging in")
    cursor = create_cursor(page)
    await cursor.random_move()
    start_url = "https://steamcommunity.com/login/home/<USER>"
    try:
        await page.goto(start_url)
    except playwright.async_api.TimeoutError as e:
        print(f"Timeout error when loading homepage: {e}")
        raise FailedToLoadInitialPage
    await asyncio.sleep(random.uniform(0.5, 2))

    print("enter username")
    login_selector = 'input:below(:text("Sign in with account name"))'
    await wait_and_click(page, cursor, login_selector)
    await page.fill(login_selector, username)
    await asyncio.sleep(random.uniform(0.5, 2))

    print("enter password")
    pw_selector = 'input:below(:text("Password"))'
    await wait_and_click(page, cursor, pw_selector)
    await page.fill(pw_selector, password)
    await asyncio.sleep(random.uniform(0.5, 2))

    cookie_future = asyncio.Future()
    cookie_handler = lambda request: cookie_interceptor(
        request, start_url + "profiles", cookie_future
    )
    page.on("request", cookie_handler)
    await asyncio.sleep(random.uniform(2, 5))
    await wait_and_click(page, cursor, 'button:text("Sign in")')
    await asyncio.sleep(random.uniform(2, 5))

    try:
        cookie = await cookie_future
    except Exception as e:
        print(f"Failed to retrieve cookie: {e}")
        return None

    return cookie


async def start_browser(email, username, password, max_retries=3):
    for attempt in range(max_retries):
        try:
            p = await get_proxy()
            proxy = {
                'server': f"{p['address']}:{p['port']}",
                'username': f"{p['username']}",
                'password': f"{p['password']}"
            }
            print(f"started antidetect for {email}")
            antidetect = await Antidetect().start()
            browser = await antidetect.new_browser(proxy_string=f"http://{proxy['username']}:{proxy['password']}@{proxy['server']}", headless=True)
            print(f"started browser for {email}")
            page = await browser.new_page()
            await page.goto("https://steamcommunity.com/")
            
            cookie = await login(page, email, username, password)
            await browser.close()
            if cookie:
                return cookie
            else:
                print(f"Failed to retrieve cookie for {email}")
                
        except Exception as e:
            print(f"Error in start_browser for {email}: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            if 'browser' in locals():
                try:
                    await browser.close()
                except:
                    pass
            
            if attempt < max_retries - 1:
                print(f"Retrying {email} (attempt {attempt + 2}/{max_retries})")
                await asyncio.sleep(random.uniform(2, 5))
            else:
                print(f"Failed to get cookie for {email}")
                
    return None


async def refresh_cookies(unblocked_accounts):
    for account in unblocked_accounts:
        account_email = account[0]
        username = account[1].strip()
        password = account[2].strip()
        print(f"getting cookies for {account_email}")
        
        cookie = await start_browser(account_email, username, password)
        if cookie:
            now = datetime.utcnow().replace(tzinfo=pytz.UTC)
            id = (qb.APIQuery(steam_libraries.account_details).where(email = account_email).fetchone_sync())['id']
            qb.APIQuery(steam_libraries.account_cookies).update_sync({'id': id}, {'last_updated': now})


async def update_cookies():
    now = datetime.now(tz=pytz.UTC)
    cookies = await qb.Query(steam_libraries.account_cookies).fetchall()
    need_updating = []
    for c in cookies:
        if c['last_updated'] < now - timedelta(hours=PROXY_TIMEOUT):
            need_updating.append(c['account_pk'])
    account_list = []
    for id in need_updating:
        account_info = []
        a = await qb.Query(steam_libraries.account_details).where(id=id).fetchone()
        account_info.append(a['email'])
        account_info.append(a['username'])
        account_info.append(a['password'])
        account_list.append(account_info)
    print("updating cookies")
    print(len(account_list))
    await refresh_cookies(account_list)

if __name__ == "__main__":
    asyncio.run(update_cookies())
