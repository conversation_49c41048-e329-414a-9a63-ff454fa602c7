import xml.etree.ElementTree as ET
import asyncio
import aiohttp
import random
import time
from datetime import datetime
import pytz
import query_builder as qb
from library.steam_library.proxies import get_proxies
from library.steam_library.get_library import get_cookies
from library.steam_library.get_library import HEADERS


# Database schemas
libraries = qb.Schema('sldb', db_name='steam_test')
steam_libraries = qb.Schema('library', db_name='steam')

async def parse_games_response(resp: aiohttp.ClientResponse):
    """Parse the XML response from Steam to extract game information"""
    try:
        xml_content = await resp.text()
        with open('library/steam_library/output.xml', 'w') as f:
            f.write(xml_content)
        
    except ET.ParseError as e:
        print(f"Failed to parse XML: {e}")
        print(f"Raw response: {xml_content}")
        return []

async def get_games_debug(pid, cookies, proxies):
    """Get games for a single Steam profile with debug output"""

    # HEADERS = {
    #     "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    #     "Accept-Encoding": "gzip, deflate, br, zstd",
    #     "Accept-Language": "en-US,en;q=0.9,en-GB;q=0.8",
    #     "Connection": "keep-alive",
    #     "Referer": f"https://steamcommunity.com/profiles/{pid}",
    #     "Sec-Fetch-Dest": "document",
    #     "Sec-Fetch-Mode": "navigate",
    #     "Sec-Fetch-Site": "same-origin",
    #     "Sec-Fetch-User": "?1",
    #     "Upgrade-Insecure-Requests": "1",
    #     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    #     "sec-ch-ua": '"Chromium";v="142", "Microsoft Edge";v="142", "Not_A Brand";v="99"',
    #     "sec-ch-ua-mobile": "?0",
    #     "sec-ch-ua-platform": '"Windows"',
    # }

    proxy = random.choice(proxies)
    p = f"http://{proxy['username']}:{proxy['password']}@{proxy['address']}:{proxy['port']}/"
    url = f"https://steamcommunity.com/profiles/{pid}/games/?tab=all&xml=1"
    # url = f"https://steamcommunity.com/profiles/{pid}?tab=all&xml=1"

    
    count = 1
    while count <= 4:
        resp = None
        timeout = aiohttp.ClientTimeout(total=8*count)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                start_time = time.time()
                resp = await session.get(url, headers=HEADERS, cookies=cookies, proxy=p, allow_redirects=False)
                request_time = time.time() - start_time

            except Exception as e:
                print(f"Request failed with error: {e}")

            if not resp:
                proxy = random.choice(proxies)
                p = f"http://{proxy['username']}:{proxy['password']}@{proxy['address']}:{proxy['port']}/"
                count += 1
                continue

            elif resp.status == 200:
                return await parse_games_response(resp)

            elif resp.status in (301, 302, 303, 307, 308):
                redirect = resp.headers.get("Location")
                print(f"→ Redirect to: {redirect}")
                resp = await session.get(redirect, headers=HEADERS, cookies=cookies, proxy=p)
                return await parse_games_response(resp)


            elif resp.status == 500:
                print("⚠ Server error (500) - possibly suspicious account")
                return []

            else:
                print(f"✗ Failed to fetch page. Status code: {resp.status}")
                count += 1

    print("All attempts failed, returning empty list")
    return []

async def test_single_account():
    test_steam_id = "*****************"

    print("=== Steam Library Debug Test ===")

    try:
        proxies = await get_proxies()

        cookies_list = await get_cookies()
        if not cookies_list:
            return

        cookie = cookies_list[0]

        games = await get_games_debug(test_steam_id, cookie, proxies)

    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
        return []

async def main():
    await test_single_account()

if __name__ == '__main__':
    import asyncio
    asyncio.run(main())