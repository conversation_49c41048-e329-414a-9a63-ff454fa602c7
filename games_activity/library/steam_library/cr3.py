import asyncio
import random
import traceback
from datetime import datetime, timedelta
import pytz
import playwright
from library.steam_library.proxies import get_proxy
import query_builder as qb
from camoufox import AsyncCamoufox, launch_options


COOKIE_TIMEOUT = 0
steam_libraries = qb.Schema('library', db_name='steam')

class FailedToLoadInitialPage(Exception):
    pass

async def cookie_interceptor(request, url, future):
    url = "https://steamcommunity.com/profiles/"
    if request.url.startswith(url):
        try:
            headers = await request.all_headers()
            cookie = headers.get("cookie")
            if not cookie:
                raise Exception("<PERSON><PERSON> not found")
            if not future.done():
                future.set_result(cookie)
        except playwright._impl._api_types.Error as e:
            print(f"cookie_interceptor: {e}")
        except Exception as e:
            print(f"cookie_interceptor: {e}")

async def cookie_refresh_camoufox(email, username, password, max_retries=5):
    if AsyncCamoufox is None:
        raise ImportError("Camoufox is not available. Please install camoufox package.")

    for attempt in range(max_retries):
        try:
            p = await get_proxy()
            proxy = {
                'server': f"http://{p['address']}:{p['port']}",
                'username': f"{p['username']}",
                'password': f"{p['password']}"
            }

            print(f"Starting Camoufox browser for {email} (attempt {attempt + 1}/{max_retries})")

            opts = launch_options(
                proxy=proxy,
                geoip=True,
                window=(1080, 720),
                humanize=True,
                headless=True,
            )


            async with AsyncCamoufox(from_options=opts, geoip=True, headless=True) as browser:
                # Create a context with your fingerprint
                context = await browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                            "AppleWebKit/537.36 (KHTML, like Gecko) "
                            "Chrome/********* Safari/537.36 Edg/*********",
                    locale="en-US",
                )
                page = await context.new_page()

                await page.set_extra_http_headers({
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                    "Accept-Language": "en-US,en;q=0.9,en-GB;q=0.8",
                    "Referer": "https://steamcommunity.com/",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                    "Upgrade-Insecure-Requests": "1",
                    "sec-ch-ua": '"Chromium";v="142", "Microsoft Edge";v="142", "Not_A Brand";v="99"',
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": '"Windows"',
                })

                # Now continue as before
                await page.goto("https://steamcommunity.com/")
                await asyncio.sleep(2)
                cookie = await login_camoufox(page, context, username, password)

                if cookie:
                    print(f"Successfully retrieved cookie for {email}")
                    return cookie
                else:
                    print(f"Failed to retrieve cookie for {email}")

        except Exception as e:
            print(f"Error in cookie_refresh_camoufox for {email}: {e}")

            if attempt < max_retries - 1:
                print(f"Retrying {email} (attempt {attempt + 2}/{max_retries})")
                await asyncio.sleep(random.uniform(2, 5))
            else:
                print(f"Failed to get cookie for {email} after {max_retries} attempts")

    return None


async def login_camoufox(page, context, username, password):
    """
    Login to Steam using Camoufox page.

    Args:
        page: Camoufox page object
        username: Steam account username
        password: Steam account password

    Returns:
        Cookie string if successful, None if failed
    """
    start_url = "https://steamcommunity.com/login/home/<USER>"

    try:
        await page.goto(start_url)
    except Exception as e:
        print(f"Timeout error when loading homepage: {e}")
        raise FailedToLoadInitialPage

    await asyncio.sleep(random.uniform(2, 4))

    print("Enter username")
    # Try multiple selectors for the username field
    username_selectors = [
        'input:below(:text("Sign in with account name"))',
        'input[type="text"]',
        '#signin-entrance-input-signinId',
        'input[name="username"]'
    ]

    username_input = None
    for selector in username_selectors:
        try:
            await page.wait_for_selector(selector, timeout=5000)
            username_input = page.locator(selector).first
            if await username_input.is_visible():
                break
        except:
            continue

    if username_input is None:
        raise Exception("Could not find username input field")

    # Focus and fill username
    await username_input.click(timeout=10000)
    await username_input.fill(username)
    await asyncio.sleep(random.uniform(1, 2))

    print("Enter password")
    # Try multiple selectors for the password field
    password_selectors = [
        'input:below(:text("Password"))',
        'input[type="password"]',
        '#signin-password-input-password',
        'input[name="password"]'
    ]

    password_input = None
    for selector in password_selectors:
        try:
            await page.wait_for_selector(selector, timeout=5000)
            password_input = page.locator(selector).first
            if await password_input.is_visible():
                break
        except:
            continue

    if password_input is None:
        raise Exception("Could not find password input field")

    # Focus and fill password
    await password_input.click(timeout=10000)
    await password_input.fill(password)
    await asyncio.sleep(random.uniform(1, 2))

    # Set up cookie interceptor
    cookie_future = asyncio.Future()
    cookie_handler = lambda request: cookie_interceptor(
        request, start_url + "profiles", cookie_future
    )
    page.on("request", cookie_handler)

    print("Clicking sign in button")
    # Try multiple selectors for the sign in button
    signin_selectors = [
        'button:text("Sign in")',
        'button[type="submit"]',
        '#signin-entrance-button',
        'button[data-qa="button-primary"]'
    ]

    signin_button = None
    for selector in signin_selectors:
        try:
            await page.wait_for_selector(selector, timeout=5000)
            signin_button = page.locator(selector).first
            if await signin_button.is_visible():
                break
        except:
            continue

    if signin_button is None:
        raise Exception("Could not find sign in button")

    await asyncio.sleep(random.uniform(2, 4))
    await signin_button.click(timeout=10000)
    await asyncio.sleep(random.uniform(3, 6))

    ###########################

    await page.wait_for_load_state("networkidle")
    url = f"https://steamcommunity.com/profiles/*****************?tab=all&xml=1"
    # url = "https://steamcommunity.com/profiles/*****************/games/?tab=all&xml=1",
    response = await context.request.get(
        url
    )

    import xml.etree.ElementTree as ET
    text = await response.text()
    # save to file
    with open('library/steam_library/output_profile.xml', 'w') as f:
        f.write(text)
    root = ET.fromstring(text)
    print(root)
    
    # await page.goto("https://steamcommunity.com/profiles/*****************/games/?tab=all&xml=1")
    # await page.wait_for_load_state("networkidle")


    ##########################

    try:
        cookie = await asyncio.wait_for(cookie_future, timeout=30)
        return cookie
    except asyncio.TimeoutError:
        print("Timeout waiting for cookie")
        return None
    except Exception as e:
        print(f"Failed to retrieve cookie: {e}")
        return None


async def refresh_cookies_camoufox(unblocked_accounts):
    """
    Refresh cookies for multiple accounts using Camoufox.

    Args:
        unblocked_accounts: List of account tuples (email, username, password)
    """
    for account in unblocked_accounts:
        account_email = account[0]
        username = account[1].strip()
        password = account[2].strip()
        print(f"Getting cookies for {account_email} using Camoufox")

        cookie = await cookie_refresh_camoufox(account_email, username, password)
        if cookie:
            now = datetime.now(tz=pytz.UTC)
            id = (qb.APIQuery(steam_libraries.account_details).where(email = account_email).fetchone_sync())['id']
            qb.APIQuery(steam_libraries.account_cookies).update_sync({'id': id}, {'last_updated': now})


async def update_cookies_camoufox():
    """
    Update cookies using Camoufox for accounts that need refreshing.
    """
    now = datetime.now(tz=pytz.UTC)
    cookies = await qb.Query(steam_libraries.account_cookies).fetchall()
    need_updating = []

    for c in cookies:
        if c['last_updated'] < now - timedelta(hours=COOKIE_TIMEOUT):
            need_updating.append(c['account_pk'])

    account_list = []
    for id in need_updating:
        account_info = []
        a = await qb.Query(steam_libraries.account_details).where(id=id).fetchone()
        account_info.append(a['email'])
        account_info.append(a['username'])
        account_info.append(a['password'])
        account_list.append(account_info)

    print(f"Updating cookies for {len(account_list)} accounts using Camoufox")
    await refresh_cookies_camoufox(account_list)


if __name__ == "__main__":
    asyncio.run(update_cookies_camoufox())
