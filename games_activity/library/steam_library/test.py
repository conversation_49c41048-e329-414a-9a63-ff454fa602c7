import xml.etree.ElementTree as ET
import asyncio
import aiohttp
import random
import time
from datetime import datetime
import pytz
import query_builder as qb
from library.steam_library.proxies import get_proxies
from library.steam_library.get_library import get_cookies
from library.steam_library.get_library import HEADERS


# Database schemas
libraries = qb.Schema('sldb', db_name='steam_test')
steam_libraries = qb.Schema('library', db_name='steam')

async def parse_games_response(resp: aiohttp.ClientResponse):
    """Parse the XML response from Steam to extract game information"""
    try:
        xml_content = await resp.text()
        # save xml_content to file
        with open('library/steam_library/output.xml', 'w') as f:
            f.write(xml_content)
        # print(f"Response status: {resp.status}")
        # print(f"Response content length: {len(xml_content)}")
        # print(f"\n{xml_content}")

        # root = ET.fromstring(xml_content)
        # games = []

        # for game in root.findall(".//games/game"):
        #     game_info = {
        #         "appID": game.find("appID").text if game.find("appID") is not None else None,
        #         "name": game.find("name").text if game.find("name") is not None else None,
        #         "hoursOnRecord": game.find("hoursOnRecord").text if game.find("hoursOnRecord") is not None else None,
        #     }
        #     games.append(game_info)

        # print(f"Found {len(games)} games")
        # return games

    except ET.ParseError as e:
        print(f"Failed to parse XML: {e}")
        print(f"Raw response: {xml_content}")
        return []

async def get_games_debug(pid, cookies, proxies):
    """Get games for a single Steam profile with debug output"""

    # HEADERS = {
    #     "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    #     "Accept-Encoding": "gzip, deflate, br, zstd",
    #     "Accept-Language": "en-US,en;q=0.9,en-GB;q=0.8",
    #     "Connection": "keep-alive",
    #     "Referer": f"https://steamcommunity.com/profiles/{pid}",
    #     "Sec-Fetch-Dest": "document",
    #     "Sec-Fetch-Mode": "navigate",
    #     "Sec-Fetch-Site": "same-origin",
    #     "Sec-Fetch-User": "?1",
    #     "Upgrade-Insecure-Requests": "1",
    #     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    #     "sec-ch-ua": '"Chromium";v="142", "Microsoft Edge";v="142", "Not_A Brand";v="99"',
    #     "sec-ch-ua-mobile": "?0",
    #     "sec-ch-ua-platform": '"Windows"',
    # }

    proxy = random.choice(proxies)
    p = f"http://{proxy['username']}:{proxy['password']}@{proxy['address']}:{proxy['port']}/"
    url = f"https://steamcommunity.com/profiles/{pid}/games/?tab=all"
    # url = f"https://steamcommunity.com/profiles/{pid}?tab=all&xml=1"

    # print(f"Fetching games for Steam ID: {pid}")
    # print(f"URL: {url}")
    # print(f"Using proxy: {proxy['address']}:{proxy['port']}")
    # print(f"Cookies: {list(cookies.keys())}")

    count = 1
    while count <= 4:
        print(f"\nAttempt {count}/4")
        resp = None
        timeout = aiohttp.ClientTimeout(total=8*count)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                start_time = time.time()
                resp = await session.get(url, headers=HEADERS, cookies=cookies, proxy=p)
                request_time = time.time() - start_time
                print(f"Request completed in {request_time:.2f} seconds")

            except Exception as e:
                print(f"Request failed with error: {e}")

            if not resp:
                print("No response received, trying different proxy")
                proxy = random.choice(proxies)
                p = f"http://{proxy['username']}:{proxy['password']}@{proxy['address']}:{proxy['port']}/"
                count += 1
                continue

            elif resp.status == 200:
                print("✓ Page fetched successfully!")
                return await parse_games_response(resp)

            elif resp.status == 500:
                print("⚠ Server error (500) - possibly suspicious account")
                return []

            else:
                print(f"✗ Failed to fetch page. Status code: {resp.status}")
                count += 1

    print("All attempts failed, returning empty list")
    return []

async def format_games_debug(pid, games):
    """Format games data for database insertion with debug output"""
    player_games = []
    current_time = datetime.now(tz=pytz.utc)

    if not games:
        print("No games to format")
        return player_games

    print(f"\nFormatting {len(games)} games for player {pid}")

    for i, game in enumerate(games):
        playtime = str(game['hoursOnRecord']) if game['hoursOnRecord'] else "0.0"

        info = {
            "pid": pid,
            "game_id": game["appID"],
            "play_time": playtime.replace(',', ''),
            "first_seen": current_time,
        }
        player_games.append(info)

        # Show first few games as examples
        if i < 5:
            print(f"  Game {i+1}: {game['name']} (ID: {game['appID']}, Hours: {playtime})")

    if len(games) > 5:
        print(f"  ... and {len(games) - 5} more games")

    return player_games

async def test_single_account():
    """Test fetching library for a single Steam account"""

    # Test Steam ID - replace with the one you want to debug
    test_steam_id = "*****************"  # Replace with actual Steam ID

    print("=== Steam Library Debug Test ===")
    print(f"Testing Steam ID: {test_steam_id}")

    try:
        print("\n1. Getting proxies...")
        proxies = await get_proxies()
        print(f"✓ Got {len(proxies)} proxies")

        print("\n2. Getting cookies...")
        cookies_list = await get_cookies()
        if not cookies_list:
            print("✗ No cookies available!")
            return

        cookies = cookies_list[0]  # Use first available cookie
        print(f"✓ Got cookies from {len(cookies_list)} accounts")

        print("\n3. Fetching games...")
        games = await get_games_debug(test_steam_id, cookies, proxies)

        print("\n4. Formatting games...")
        formatted_games = await format_games_debug(test_steam_id, games)

        print(f"\n=== RESULTS ===")
        print(f"Steam ID: {test_steam_id}")
        print(f"Games found: {len(games)}")
        print(f"Formatted entries: {len(formatted_games)}")

        if formatted_games:
            print(f"\nSample formatted game data:")
            for game in formatted_games[:3]:
                print(f"  - Game ID: {game['game_id']}, Play time: {game['play_time']} hours")

        return formatted_games

    except Exception as e:
        print(f"✗ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return []

async def main():
    """Main function to run the single account test"""
    # You can modify the Steam ID here for testing
    await test_single_account()

if __name__ == '__main__':
    import asyncio
    asyncio.run(main())