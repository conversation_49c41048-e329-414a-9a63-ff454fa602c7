import asyncio
from asyncio import Queue, QueueEmpty
from datetime import datetime
import pytz
import random
import time
import isodate

from library.playstation_library.helpers.auth_edit import Authenticator
from library.playstation_library.helpers.async_request_builder import AsyncRequestBuilder
from library.playstation_library.helpers.utils import get_proxies, get_tokens, get_panel
import query_builder as qb

from logging_utils import init_logger

logger = init_logger()

NUMBER_OF_TOKENS = 1

ps_db = qb.db(db_name='ps')
ps_library = qb.Schema('library', db_name='ps')
_game_cache = {}
_catagory_cache = {}

async def preload_cache():
    query = qb.Query(ps_library.user_games).select('id', 'name').distinct()
    rows = await query.fetchall()
    for r in rows:
        _game_cache[r['name']] = r['id']

    query = qb.Query(ps_library.platform_base).select('platform_id', 'platform_name').distinct()
    rows = await query.fetchall()
    for r in rows:
        _catagory_cache[r['platform_name']] = r['platform_id']

async def get_library(account_id: int, builder: AsyncRequestBuilder, sessions):
    """Get PlayStation library for a single account"""
    base_uri = f'https://m.np.playstation.com/api/gamelist/v2/users/{account_id}/titles'
    params = {
        'categories': 'ps4_game,ps5_native_game',
        'limit': 200,   # max allowed
        'offset': 0
    }

    all_titles = []
    retries = 3

    while True:
        session = random.choice(sessions)
        count = 1
        while count <= retries:
            try:
                response = await builder.get(url=base_uri, session=session, params=params)
                break
            except Exception as e:
                if count == retries:
                    print(f"Failed to get library for account {account_id} after {retries} attempts: {e}")
                    return []
                await asyncio.sleep(1)
                count += 1

        items = response.get("name", [])
        all_titles.extend(items)

        if len(items) < params["limit"]:
            break

        params["offset"] += params["limit"]

    return all_titles

async def get_game_id(game_name):
    """Get game ID from cache or database"""
    if game_name in _game_cache:
        return _game_cache[game_name]
    else:
        game_id = await insert_game(game_name)
        _game_cache[game_name] = game_id
        return game_id
    
async def insert_game(game_name):
    """Insert game into database and return ID"""
    insert_stmt = qb.insert(ps_library.game_base).values({'name': game_name})
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing()
        .returning(ps_library.game_base.c.id)
    )
    rows = await qb.engine(insert_stmt, db_name='ps').execute()
    for r in rows:
        game_id, _ = r
        return game_id

async def get_category_id(category_name):
    """Get category ID from cache or database"""
    if category_name in _catagory_cache:
        return _catagory_cache[category_name]
    else:
        category_id = await insert_category(category_name)
        _catagory_cache[category_name] = category_id
        return category_id
    
async def insert_category(category_name):
    """Insert category into database and return ID"""
    insert_stmt = qb.insert(ps_library.platform_base).values({'platform_name': category_name})
    insert_stmt = (
        insert_stmt
        .on_conflict_do_nothing()
        .returning(ps_library.platform_base.c.platform_id)
    )
    rows = await qb.engine(insert_stmt, db_name='ps').execute()
    for r in rows:
        platform_id, _ = r
        return platform_id

async def format_games(account_id, games) -> list[dict]:
    """Format PlayStation games data for database insertion"""
    player_games = []

    if games:
        for game in games:
            # Convert ISO 8601 duration to total seconds
            play_duration_iso = game.get("playDuration", "PT0S")
            try:
                play_duration_seconds = int(isodate.parse_duration(play_duration_iso).total_seconds())
                play_time_hours = play_duration_seconds / 3600.0  # Convert to hours like Steam
            except:
                play_time_hours = 0.0

            game_id = await get_game_id(game['name'])
            category_id = await get_category_id(game['category'])

            info = {
                "user_id": account_id,
                "game_id": game_id,
                "platform_id": category_id,
                "play_count": game.get("playCount", 0),
                "play_time": play_time_hours,
                "first_played": game.get("firstPlayedDateTime"),
                "last_played": game.get("lastPlayedDateTime"),
                "first_seen": datetime.now(tz=pytz.utc)
            }
            player_games.append(info)

    return player_games

async def insert_games(result_queue: Queue, formatted_games: list[dict], seen_queue: Queue):
    """Insert games into result queue and process when queue is large enough"""
    now = datetime.now(tz=pytz.utc)
    if not formatted_games:
        return

    for fg in formatted_games:
        await result_queue.put(fg)

    if result_queue.qsize() < 250:
        return

    to_insert = []
    while not result_queue.empty():
        to_insert.append(result_queue.get_nowait())

    await insert_user_games(to_insert)

    to_update = []
    while not seen_queue.empty():
        to_update.append({'user_id': seen_queue.get_nowait(), 'last_updated': now, 'first_seen': now})

    if len(to_update) > 0:
        await insert_active_profiles(to_update)

async def insert_user_games(to_insert: list):
    """Insert user games with conflict resolution"""
    insert_stmt = qb.insert(ps_library.user_games).values(to_insert)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_update(
            index_elements=[ps_library.user_games.c.user_id, ps_library.user_games.c.title_id],
            set_={
                'play_time_hours': insert_stmt.excluded.play_time_hours,
                'play_count': insert_stmt.excluded.play_count,
                'last_played': insert_stmt.excluded.last_played
            }
        )
    )
    await qb.engine(insert_stmt, db_name='ps').execute()

async def insert_active_profiles(to_update: list):
    """Insert/update active profiles with last updated timestamp"""
    insert_stmt = qb.insert(ps_library.active_profiles).values(to_update)
    insert_stmt = (
        insert_stmt
        .on_conflict_do_update(
            index_elements=[ps_library.active_profiles.c.account_id],
            set_={'last_updated': insert_stmt.excluded.last_updated}
        )
    )
    await qb.engine(insert_stmt, db_name='ps').execute()

async def process(account_queue: Queue, result_queue: Queue, seen_queue: Queue, builder: AsyncRequestBuilder, proxies):
    """Process accounts from queue using a single builder"""
    while True:
        try:
            account_id = account_queue.get_nowait()
        except QueueEmpty:
            break

        while True:
            try:
                t0 = time.time()
                games = await get_library(account_id, builder, proxies)
                formatted_games = await format_games(account_id, games)
                seen_queue.put_nowait(account_id)
                await insert_games(result_queue, formatted_games, seen_queue)
                t1 = time.time() - t0
                if t1 > 100:
                    print(f'Account {account_id} took {t1:.2f}s')
                break
            except asyncio.TimeoutError:
                print(f"Timeout for account {account_id}")
                continue
            except Exception as e:
                print(f"Error processing account {account_id}: {e}")
                continue

async def main(account_ids, proxies, builders):
    """Main processing function with queues and concurrent workers"""
    await preload_cache()
    account_queue = Queue()
    for account_id in account_ids:
        account_queue.put_nowait(account_id)

    result_queue = Queue()
    seen_queue = Queue()
    start_time = time.time()

    # Create tasks for each builder
    tasks = []
    for builder in builders:
        task = process(account_queue, result_queue, seen_queue, builder, proxies)
        tasks.append(task)

    await asyncio.gather(*tasks)

    # Process remaining items in queues
    to_insert = []
    while not result_queue.empty():
        to_insert.append(result_queue.get_nowait())
    if len(to_insert) > 0:
        await insert_user_games(to_insert)

    to_update = []
    now = datetime.now(tz=pytz.utc)
    while not seen_queue.empty():
        to_update.append({'account_id': seen_queue.get_nowait(), 'last_updated': now})
    if len(to_update) > 0:
        await insert_active_profiles(to_update)

    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"Batch completed in {elapsed_time:.2f} seconds.")
    return elapsed_time

async def run_main():
    """Main entry point following Steam library pattern"""
    print("Starting")
    await asyncio.sleep(5)

    print("Getting proxies")
    proxies = await get_proxies()

    print("Getting tokens")
    tokens = await get_tokens(number_of_tokens=NUMBER_OF_TOKENS)
    print(f"Got {len(tokens)} tokens")

    # Create builders from tokens
    builders = []
    for i, token in enumerate(tokens):
        try:
            authenticator = await Authenticator.async_init(npsso_token=token, proxy_pool=proxies, logger=logger)
            if authenticator.success:
                builder = AsyncRequestBuilder(authenticator)
                builders.append(builder)
                print(f"Successfully created builder {i+1}")
            else:
                print(f"Failed to authenticate token {i+1}")
        except Exception as e:
            print(f"Error creating authenticator {i+1}: {e}")

    print(f"Running with {len(builders)} builders")
    print(f"Running with {len(proxies)} proxies")

    if not builders:
        print("No working builders available!")
        return

    # Get panel of account IDs
    panel = await get_panel()
    print(f"Got {len(panel)} accounts from panel")
    panel = panel[:50]

    batch_size = 1000
    total_time = 0
    total_processed = 0

    try:
        for i in range(0, len(panel), batch_size):
            print(f"Starting batch {i // batch_size + 1}")
            account_batch = panel[i:i + batch_size]
            batch_time = await main(account_batch, proxies, builders)
            total_time += batch_time
            total_processed += len(account_batch)
            print("-----------------------------------------")
            print(f"Processed {total_processed} accounts in {total_time:.2f} seconds")
            print(f"{total_processed/total_time:.2f} accounts processed per second")
            print(f"{total_time/total_processed:.2f} seconds per account")
            print("-----------------------------------------")

    except Exception as e:
        print(f"Error in main processing: {e}")
    finally:
        # Clean up proxy sessions
        if proxies:
            for session in proxies:
                try:
                    await session.close()
                except Exception as e:
                    print(f"Error closing session: {e}")

if __name__ == "__main__":
    print("------------")
    print("start")
    t0 = time.time()
    asyncio.run(run_main())
    print(f'took {time.time()-t0}s')








