from panel.playstation.get_unmonitored_accounts import get_tokens
import asyncio
import query_builder as qb

from datetime import datetime, timedelta

from panel.playstation.helpers.proxy_handler_client import ProxySession
from logging_utils import init_logger, logging

logger = init_logger()

ps_db = qb.db(db_name='ps')
ps_public = qb.Schema('public', db_name='ps')

async def get_proxy_request(number=20):
    import requests
    import random
    url = "https://proxy.ampere-analytics.com/proxy_api/proxies/proxy?proxy_type=datacenter&proxy_type=vpn&country_code=MX&country_code=US"

    proxy_list = []
    for _ in range(number):
        response = requests.get(url)

        
        if response.status_code == 200:
            included_providers = ['rayobyte']
            proxies = response.json()  # or .text if it’s plain text
            proxies = [p for p in proxies if p['provider']['name'] in included_providers]
            while True:
                prox = random.choice(proxies)
                p = prox['proxy']
                cred = prox['provider']['credentials']
                if cred:
                    proxy = {'ip': p['ip'], 'port': p['port'], 'username': cred['username'], 'password': cred['password']}
                    proxy_list.append(proxy)
                    break
        else:
            print(f"Error {response.status_code}: {response.text}")
    return proxy_list

async def get_proxies():
    all_proxies_sessions = []
    proxies = await get_proxy_request()
    for proxy in proxies:
        proxy_data = {
            "proxy": {
                "protocol": "http",
                "ip": proxy['ip'],
                "port": proxy['port'],
                "ssl": False,
                "unique_proxy_id": "custom-proxy-1"
            },
            "provider": {
                "credentials": {
                    "username": proxy['username'],
                    "password": proxy['password']
                },
                "name": "custom"
            }
        }

        proxy_session = await ProxySession.async_init_from_data(
            logger=logging.getLogger(),
            proxy_data=proxy_data,
            do_not_update=True
        )
        all_proxies_sessions.append(proxy_session)
    
    return all_proxies_sessions

async def get_tokens(number_of_tokens=1):
    import random
    table = ps_public.accounts_sso
    query = qb.Query(
        qb.select(table.c.sso)
        .where(
            table.c.updated_at > datetime.now() - timedelta(days=10)
        )
        .order_by(table.c.updated_at.desc())
        .limit(number_of_tokens*5)
    )
    results = await query.fetchall()
    results = [r['sso'] for r in results]
    tokens = []
    for i in range(number_of_tokens):
        token = random.choice(results)
        results.remove(token)
        tokens.append(token)
    return tokens

async def get_panel():
    query = qb.Query(
        qb.select(ps_public.new_panel_users)
    )
    results = await query.fetchall()
    results = [r['account_id'] for r in results]
    return results