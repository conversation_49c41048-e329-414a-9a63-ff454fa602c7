import query_builder as qb

class PSLibrarySchema(qb.Schema):
    _db_name = 'ps'
    _schema = 'library'

    class user_games(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('account_id', qb.BIGINT),
            qb.TableColumn('title_id', qb.TEXT),
            qb.TableColumn('name', qb.TEXT),
            qb.TableColumn('category', qb.TEXT),
            qb.TableColumn('play_count', qb.INTEGER, default=0),
            qb.TableColumn('play_time_hours', qb.FLOAT, default=0.0),
            qb.TableColumn('first_played', qb.TEXT, nullable=True),
            qb.TableColumn('last_played', qb.TEXT, nullable=True),
            qb.TableColumn('first_seen', qb.TIMESTAMPTZ),
        ]
        _constraints = [qb.UniqueConstraint('account_id', 'title_id')]

    class active_profiles(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('account_id', qb.BIGINT),
            qb.TableColumn('last_updated', qb.TIMESTAMPTZ),
        ]
        _constraints = [qb.UniqueConstraint('account_id')]

    class game_base(qb.Table):
        _columns = [
            qb.ID(),
            qb.TableColumn('title_id', qb.TEXT),
            qb.TableColumn('name', qb.TEXT),
            qb.TableColumn('category', qb.TEXT, nullable=True),
        ]
        _constraints = [qb.UniqueConstraint('title_id')]

ps_library = PSLibrarySchema()
