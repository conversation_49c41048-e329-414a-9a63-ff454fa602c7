import query_builder as qb
import asyncio
from datetime import datetime
import pandas as pd
import pytz
import time
from database_management.output_analysis import get_game_df

discord_db = qb.db(db_name='discord')
discord_public = qb.Schema('public', db_name='discord')

_act_state_cache = {}
_act_details_cache = {}
_guild_cache = {}
_game_cache = {}
_state_cache = {}
_presence_cache = {}


async def preload_cache():
    """Preload all existing lookup tables into memory."""

    preload_targets = [
        ('game', _game_cache, discord_public.game_base, 'game_id', 'game_name'),
        ('act_state', _act_state_cache, discord_public.act_state_base, 'act_state_id', 'act_state'),
        ('act_details', _act_details_cache, discord_public.act_details_base, 'act_details_id', 'act_details'),
        ('guild', _guild_cache, discord_public.guild_base, 'guild_id', 'guild_name'),
        ('state', _state_cache, discord_public.state_base, 'state_id', None),
    ]

    for name, cache, table, id_col, name_col in preload_targets:
        if name == 'state':
            query = qb.Query(table).select(
                table.c.state_id,
                table.c.act_state_id,
                table.c.act_details_id
            )
            rows = await query.fetchall()
            for r in rows:
                cache[(r['act_state_id'], r['act_details_id'])] = r['state_id']
        else:
            id_column = getattr(table.c, id_col)
            name_column = getattr(table.c, name_col)

            query = qb.Query(table).select(id_column, name_column)
            rows = await query.fetchall()

            for r in rows:
                cache[r[name_col]] = r[id_col]

async def game_insert(games):
    if not games: return
    formatted = [{'game_name': g} for g in games]
    stmt = qb.insert(discord_public.game_base).values(formatted)
    stmt = stmt.on_conflict_do_nothing(index_elements=[discord_public.game_base.c.game_name])
    stmt = stmt.returning(discord_public.game_base.c.game_id, discord_public.game_base.c.game_name)
    rows = await qb.engine(stmt, db_name='discord').execute()
    for r in rows:
        game_id, game_name = r
        _game_cache[game_name] = game_id

async def act_state_insert(states):
    if not states: return
    formatted = [{'act_state': s} for s in states]
    stmt = qb.insert(discord_public.act_state_base).values(formatted)
    stmt = stmt.on_conflict_do_nothing(index_elements=[discord_public.act_state_base.c.act_state])
    stmt = stmt.returning(discord_public.act_state_base.c.act_state_id, discord_public.act_state_base.c.act_state)
    rows = await qb.engine(stmt, db_name='discord').execute()
    for r in rows:
        act_state_id, act_state = r
        _act_state_cache[act_state] = act_state_id

async def act_details_insert(details):
    if not details: return
    formatted = [{'act_details': d} for d in details]
    stmt = qb.insert(discord_public.act_details_base).values(formatted)
    stmt = stmt.on_conflict_do_nothing(index_elements=[discord_public.act_details_base.c.act_details])
    stmt = stmt.returning(discord_public.act_details_base.c.act_details_id, discord_public.act_details_base.c.act_details)
    rows = await qb.engine(stmt, db_name='discord').execute()
    for r in rows:
        act_details_id, act_details = r
        _act_details_cache[act_details] = act_details_id

async def state_insert(states):
    if not states: return
    formatted = []
    for act_state_name, act_details_name in states:
        act_state_id = _act_state_cache.get(act_state_name)
        act_details_id = _act_details_cache.get(act_details_name)
        if act_state_id and act_details_id:
            formatted.append({'act_state_id': act_state_id, 'act_details_id': act_details_id})
    stmt = qb.insert(discord_public.state_base).values(formatted)
    stmt = stmt.on_conflict_do_nothing(
        index_elements=[discord_public.state_base.c.act_state_id, discord_public.state_base.c.act_details_id]
    )
    stmt = stmt.returning(discord_public.state_base.c.state_id, discord_public.state_base.c.act_state_id, discord_public.state_base.c.act_details_id)
    rows = await qb.engine(stmt, db_name='discord').execute()
    for r in rows:
        state_id, act_state_id, act_details_id = r
        _state_cache[(act_state_id, act_details_id)] = state_id

async def guild_insert(guilds):
    if not guilds: return
    formatted = [{'guild_name': g} for g in guilds]
    stmt = qb.insert(discord_public.guild_base).values(formatted)
    stmt = stmt.on_conflict_do_nothing(index_elements=[discord_public.guild_base.c.guild_name])
    stmt = stmt.returning(discord_public.guild_base.c.guild_id, discord_public.guild_base.c.guild_name)
    rows = await qb.engine(stmt, db_name='discord').execute()
    for r in rows:
        guild_id, guild_name = r
        _guild_cache[guild_name] = guild_id

async def player_insert(players):
    if not players: return
    now = datetime.now(tz=pytz.UTC)
    formatted = [{'user_id': p, 'first_seen': now} for p in players]
    stmt = qb.insert(discord_public.player_base).values(formatted)
    stmt = stmt.on_conflict_do_nothing(index_elements=[discord_public.player_base.c.user_id])
    await qb.engine(stmt, db_name='discord').execute()

async def player_guild_map_insert(player_guild_map):
    if not player_guild_map: return
    now = datetime.now(tz=pytz.UTC)
    formatted = []
    for user_id, guild_name in player_guild_map:
        guild_id = _guild_cache.get(guild_name)
        if guild_id:
            formatted.append({
                'user_id': user_id,
                'guild_id': guild_id,
                'first_seen': now,
                'last_seen': now
            })

    if (await qb.table_exists('public', 'temp_player_guild_map', db_name='discord')):
        await discord_public.player_guild_map.drop()

    temp_table: qb.Table = qb.create_table(
        schema='public',
        name='temp_player_guild_map',
        columns =[
            qb.TableColumn('user_id', qb.BIGINT),
            qb.TableColumn('guild_id', qb.BIGINT),
            qb.TableColumn('first_seen', qb.TIMESTAMP(timezone=True)),
            qb.TableColumn('last_seen', qb.TIMESTAMP(timezone=True))
        ],
        db_name='discord'
    )

    async with temp_table.copy_in() as copy_in:
        for f in formatted:
            await copy_in.write_row([f['user_id'], f['guild_id'], f['first_seen'], f['last_seen']])

    upsert_sql = """
        INSERT INTO public.player_guild_map AS pgm (user_id, guild_id, first_seen, last_seen)
        SELECT t.user_id, t.guild_id, t.first_seen, t.last_seen
        FROM public.temp_player_guild_map t
        ON CONFLICT (user_id, guild_id)
        DO UPDATE SET last_seen = EXCLUDED.last_seen;
    """
    await qb.engine(upsert_sql, db_name='discord').execute()

    await temp_table.drop()

async def presence_insert(presences):
    if not presences: return
    formatted = []
    day = datetime.now(tz=pytz.UTC).date()
    current_time = datetime.now(tz=pytz.UTC)
    for p in presences:
        game_id = _game_cache.get(p['game_name'])
        act_state_id = _act_state_cache.get(p['act_state_name'])
        act_details_id = _act_details_cache.get(p['act_details_name'])
        state_id = _state_cache.get((act_state_id, act_details_id))
        if game_id and state_id:
            formatted.append({
                'player_id': p['player_id'],
                'game_id': game_id,
                'state_id': state_id,
                'time_window': p['time_window'],
                'hits': 1,
                'day': day
            })

    if (await qb.table_exists('public', 'temp_presence', db_name='discord')):
        await discord_public.temp_presence.drop()
    
    temp_table: qb.Table = qb.create_table(
        schema='public',
        name='temp_presence',
        columns =[
            qb.TableColumn('player_id', qb.BIGINT),
            qb.TableColumn('game_id', qb.BIGINT),
            qb.TableColumn('state_id', qb.BIGINT),
            qb.TableColumn('time_window', qb.INTEGER),
            qb.TableColumn('hits', qb.INTEGER),
            qb.TableColumn('day', qb.DATE)
        ],
        db_name='discord'
    )

    async with temp_table.copy_in() as copy_in:
        for f in formatted:
            await copy_in.write_row([f['player_id'], f['game_id'], f['state_id'], f['time_window'], f['hits'], f['day']])

    upsert_sql = """
        INSERT INTO public.presence_test (player_id, game_id, state_id, time_window, hits, day)
        SELECT player_id, game_id, state_id, time_window, SUM(hits), day
        FROM public.temp_presence
        GROUP BY player_id, game_id, state_id, time_window, day
        ON CONFLICT (player_id, game_id, state_id, time_window, day)
        DO UPDATE SET hits = public.presence_test.hits + EXCLUDED.hits
        RETURNING presence_id;
    """
    rows = await qb.engine(upsert_sql, db_name='discord').execute()
    await temp_table.drop()

    presence_ids = [int(p[0]) for p in rows]
    await presence_records_insert(presence_ids, current_time)

async def presence_records_insert(presence_ids: list[int], time_stamp: datetime):
    formatted = [{'presence_id': p, 'timestamp': time_stamp} for p in presence_ids]
    stmt = qb.insert(discord_public.presence_records_test).values(formatted)
    await qb.engine(stmt, db_name='discord').execute()

async def load_data():
    df = pd.read_csv('test_outputs/games.csv')
    return df

async def format_data(df: pd.DataFrame):
    data = []
    current_hour = datetime.now(tz=pytz.UTC).hour
    # time window ranges from 0-5 in 4 hour chunks
    time_window = current_hour // 4
    for _, row in df.iterrows():
        def safe(v): return "" if pd.isna(v) else v
        data.append({
            'player_id': safe(row.get('User_ID')),
            'game_name': safe(row.get('Act_Name')),
            'act_state_name': safe(row.get('Act_State')),
            'act_details_name': safe(row.get('Act_Details')),
            'guild_name': safe(row.get('Guild_Name')),
            'time_window': time_window
        })
    return data

async def insert_data(data):
    games = list({d['game_name'] for d in data if d['game_name']})
    act_states = list({d['act_state_name'] for d in data if d['act_state_name']})
    act_details = list({d['act_details_name'] for d in data if d['act_details_name']})
    guilds = list({d['guild_name'] for d in data if d['guild_name']})
    players = list({d['player_id'] for d in data if d['player_id']})
    state_pairs = list({(d['act_state_name'], d['act_details_name']) for d in data})
    player_guild_map = list({(d['player_id'], d['guild_name']) for d in data})

    await game_insert(games)
    await act_state_insert(act_states)
    await act_details_insert(act_details)
    await state_insert(state_pairs)
    await guild_insert(guilds)
    await player_insert(players)
    await player_guild_map_insert(player_guild_map)
    await presence_insert(data)



async def main():
    path = "output.csv"
    print("Starting data load and insert...")
    df = await get_game_df(path)
    data = await format_data(df)
    print(f"inserting {len(data)} records")
    await preload_cache()
    await insert_data(data)

if __name__ == "__main__":
    t0 = time.time()
    asyncio.run(main())
    t1 = time.time()
    print(f"Total Time: {t1 - t0:.2f}s")
