path = "output_test.csv"
import pandas as pd
import asyncio

async def pp(list):
    for item in list:
        print(item)

async def get_df(filepath: str = path) -> pd.DataFrame:
    df = pd.read_csv(filepath)
    return df

async def get_unique_act_names(df: pd.DataFrame):
    unique_act_names = df['Act_Name'].unique()
    return unique_act_names

async def filter_nan(df: pd.DataFrame, column: str):
    df = df[df[column].notna()]
    return df

async def filter_df(df: pd.DataFrame, column: str, value, invert=False):
    if invert:
        df = df[df[column] != value]
    else:
        df = df[df[column] == value]
    return df

async def get_tally(df: pd.DataFrame, column: str):
    tally = df[column].value_counts()
    return tally

async def print_tally(tally: pd.Series):
    for value, count in tally.items():
        print(f"{value}: {count}")

async def save_tally(tally: pd.Series, filename: str):
    tally.to_csv(filename)

async def save_df(df: pd.DataFrame, filename: str):
    df.to_csv(filename)

async def print_df(columns: list[str], df: pd.DataFrame):
    print(df[columns].head(20))

async def print_unique(column: str, df: pd.DataFrame):
    await pp(df[column].unique())

async def get_game_df(filepath: str = path):
    df = await get_df(filepath)
    df = await filter_df(df, 'Is_Bot', False)
    df = await filter_df(df, 'Main_Status', 'online')
    df = await filter_df(df, 'Act_Type', 0)
    return df

async def main():
    df = await get_df()
    df = await filter_df(df, 'Is_Bot', False)
    df = await filter_df(df, 'Main_Status', 'online')
    df = await filter_df(df, 'Act_Type', 4, invert=True)
    df = await filter_df(df, 'Act_Type', 6, invert=True)
    df = await filter_df(df, 'Act_Type', 0)
    await save_df(df, 'test_outputs/games.csv')

if __name__ == "__main__":
    asyncio.run(main())
